import { fetchApi } from '@/libs/utils/api';
import type { GetDataWithPagination } from '@/types/utility';

export interface PreviouslyPurchasedItem {
  productOfferId: string;
  productId: string;
  productName: string;
  vendorId: string;
  vendorName: string;
  lastOrderedAt: string;
  quantity: number;
  price: number;
  imageUrl: string;
  orderCount: number;
  stockStatus: string;
  increments: number;
  isPurchasable: boolean;
}

export interface FetchPreviouslyPurchasedParams {
  search?: string;
  perPage?: number;
  sortBy?: 'last_ordered' | 'frequency' | 'name';
  page?: number;
}

interface FetchPreviouslyPurchasedProps {
  params: FetchPreviouslyPurchasedParams;
  beforeStart?: VoidFunction;
  onSuccess?: (data: { items: PreviouslyPurchasedItem[]; total: number; currentPage: number; lastPage: number }) => void;
  onError?: VoidFunction;
  afterDone?: VoidFunction;
}

const parseQueryParams = (params: FetchPreviouslyPurchasedParams): string => {
  const searchParams = new URLSearchParams();

  if (params.search) {
    searchParams.append('search', params.search);
  }

  if (params.perPage) {
    searchParams.append('per_page', params.perPage.toString());
  }

  if (params.sortBy) {
    searchParams.append('sort_by', params.sortBy);
  }

  if (params.page) {
    searchParams.append('page', params.page.toString());
  }

  return searchParams.toString();
};

export const fetchPreviouslyPurchased = async ({
  params,
  beforeStart = () => {},
  onSuccess = () => {},
  onError = () => {},
  afterDone = () => {},
}: FetchPreviouslyPurchasedProps) => {
  beforeStart();

  const queryString = parseQueryParams(params);
  try {
    const response = await fetchApi<
      GetDataWithPagination<PreviouslyPurchasedItem>
    >(`/previously-purchased?${queryString}`);

    onSuccess({
      items: response.data,
      total: response.meta.total,
      currentPage: response.meta.current_page,
      lastPage: response.meta.last_page,
    });
  } catch {
    onError();
  }

  afterDone();
};
