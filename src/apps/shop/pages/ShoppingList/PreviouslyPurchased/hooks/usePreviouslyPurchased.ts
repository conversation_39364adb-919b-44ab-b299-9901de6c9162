import { useState, useCallback } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useClinicStore } from '@/apps/shop/stores/useClinicStore';
import { useDebounce } from '@/libs/utils/hooks/useDebounce/useDebounce';
import { queryKeys } from '@/libs/query/queryClient';
import {
  fetchPreviouslyPurchased,
  type PreviouslyPurchasedItem,
  type FetchPreviouslyPurchasedParams,
} from '../services/fetchPreviouslyPurchased';

interface UsePreviouslyPurchasedReturn {
  items: PreviouslyPurchasedItem[];
  isLoading: boolean;
  error: Error | null;
  total: number;
  currentPage: number;
  lastPage: number;
  searchQuery: string;
  sortBy: 'last_ordered' | 'frequency' | 'name';
  perPage: number;
  setSearchQuery: (query: string) => void;
  setSortBy: (sort: 'last_ordered' | 'frequency' | 'name') => void;
  setPerPage: (perPage: number) => void;
  setPage: (page: number) => void;
  refetch: () => void;
}

export const usePreviouslyPurchased = (): UsePreviouslyPurchasedReturn => {
  const { clinic } = useClinicStore();
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<'last_ordered' | 'frequency' | 'name'>('last_ordered');
  const [perPage, setPerPage] = useState(20);
  const [page, setPage] = useState(1);
  const [items, setItems] = useState<PreviouslyPurchasedItem[]>([]);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [lastPage, setLastPage] = useState(1);

  const debouncedSearchQuery = useDebounce(searchQuery, 300);

  const params: FetchPreviouslyPurchasedParams = {
    search: debouncedSearchQuery || undefined,
    perPage,
    sortBy,
    page,
  };

  const queryKey = queryKeys.products.previouslyPurchased(
    clinic?.id || '',
    params
  );

  const {
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey,
    queryFn: async () => {
      if (!clinic?.id) {
        throw new Error('No clinic selected');
      }

      return new Promise<void>((resolve, reject) => {
        fetchPreviouslyPurchased({
          params,
          onSuccess: (data) => {
            setItems(data.items);
            setTotal(data.total);
            setCurrentPage(data.currentPage);
            setLastPage(data.lastPage);
            resolve();
          },
          onError: () => {
            reject(new Error('Failed to fetch previously purchased items'));
          },
        });
      });
    },
    enabled: !!clinic?.id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const handleSetSearchQuery = useCallback((query: string) => {
    setSearchQuery(query);
    setPage(1); // Reset to first page when searching
  }, []);

  const handleSetSortBy = useCallback((sort: 'last_ordered' | 'frequency' | 'name') => {
    setSortBy(sort);
    setPage(1); // Reset to first page when sorting changes
  }, []);

  const handleSetPerPage = useCallback((newPerPage: number) => {
    setPerPage(newPerPage);
    setPage(1); // Reset to first page when per page changes
  }, []);

  const handleSetPage = useCallback((newPage: number) => {
    setPage(newPage);
  }, []);

  return {
    items,
    isLoading,
    error,
    total,
    currentPage,
    lastPage,
    searchQuery,
    sortBy,
    perPage,
    setSearchQuery: handleSetSearchQuery,
    setSortBy: handleSetSortBy,
    setPerPage: handleSetPerPage,
    setPage: handleSetPage,
    refetch,
  };
};
