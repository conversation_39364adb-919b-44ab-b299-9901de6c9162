import { useState } from 'react';
import { HeaderSearch } from '@/libs/ui/HeaderSearch/HeaderSearch';
import { Pagination } from '@/libs/ui/Pagination/Pagination';
import { ShoppingListItem } from '../components/ShoppingListItem/ShoppingListItem';
import { ProductType } from '@/types';

const mockPreviouslyPurchasedItems: ProductType[] = [
  {
    id: '1',
    name: '<PERSON>stiFen (Carprofen) Injection for Dogs, 25mL',
    imageUrl: null,
    isFavorite: false,
    manufacturer: '<PERSON><PERSON>',
    manufacturerSku: '159726',
    description: null,
    attributes: [],
    isHazardous: false,
    requiresPrescription: false,
    requiresColdShipping: false,
    isControlledSubstance: false,
    isControlled222Form: false,
    requiresPedigree: false,
    offers: [
      {
        id: 'offer-1',
        vendor: {
          id: 'vendor-1',
          name: '<PERSON><PERSON>',
          imageUrl: '',
          type: 'manufacturer' as const,
        },
        name: '<PERSON><PERSON><PERSON><PERSON><PERSON> (Carprofen) Injection for Dogs, 25mL',
        vendorSku: '159726',
        price: '60.25',
        clinicPrice: '60.25',
        stockStatus: 'IN_STOCK' as const,
        lastOrderedAt: '2025-04-05',
        lastOrderedQuantity: 10,
        isPurchasable: true,
        increments: 1,
        isRecommended: true,
        rebatePercent: null,
        product: {} as ProductType, // Will be set after creation
        unitOfMeasure: 'mL',
        size: 25,
      },
    ],
  },
];

mockPreviouslyPurchasedItems.forEach((product) => {
  product.offers.forEach((offer) => {
    offer.product = product;
  });
});

export const PreviouslyPurchased = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [page, setPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState('12');

  // const filteredItems = mockPreviouslyPurchasedItems.filter((item) =>
  //   item.name.toLowerCase().includes(searchQuery.toLowerCase()),
  // );

  const total = 510;
  const displayedItems = Array.from({ length: 7 }, (_, index) => ({
    ...mockPreviouslyPurchasedItems[0],
    id: `item-${index + 1}`,
  }));

  const handleSearch = (value: string) => {
    setSearchQuery(value);
    setPage(1);
  };

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handleItemsPerPageChange = (newItemsPerPage: string) => {
    setItemsPerPage(newItemsPerPage);
    setPage(1);
  };

  return (
    <div className="w-full">
      <div className="mb-6 flex items-center justify-between rounded-lg border border-black/[0.06] bg-white p-5">
        <div className="flex flex-col gap-1">
          <h2 className="text-sm font-semibold">
            Your Previously Purchased Items
          </h2>
          <div className="flex items-center gap-4 text-xs text-black/50">
            <span>
              Total of Items:{' '}
              <span className="font-semibold text-black/70">2,450</span>
            </span>
            <div className="divider-v"></div>
            <span>
              Last Update:{' '}
              <span className="font-semibold text-black/70">04/05/2025</span>
            </span>
          </div>
        </div>

        <HeaderSearch
          placeholder="Search item"
          value={searchQuery}
          onChange={handleSearch}
          onEnter={handleSearch}
          rootClassName="w-80"
        />
      </div>

      <div className="mb-6 flex flex-col gap-1 rounded-lg bg-[#F8FBFD] p-4">
        {displayedItems.map((item) => (
          <ShoppingListItem
            key={item.id}
            product={item}
            variant="previouslyPurchased"
            showPromoIcon={false}
            showBadge={false}
            isHighlighted={
              !!searchQuery &&
              item.name.toLowerCase().includes(searchQuery.toLowerCase())
            }
          />
        ))}
      </div>

      <Pagination
        page={page}
        onPageChange={handlePageChange}
        onChangeItemsPerPage={handleItemsPerPageChange}
        itemsPerPage={itemsPerPage}
        limitOptions={[
          { value: '12', label: '12' },
          { value: '24', label: '24' },
          { value: '48', label: '48' },
          { value: '60', label: '60' },
        ]}
        total={total}
      />
    </div>
  );
};
