import { HeaderSearch } from '@/libs/ui/HeaderSearch/HeaderSearch';
import { Pagination } from '@/libs/ui/Pagination/Pagination';
import { ShoppingListItem } from '../components/ShoppingListItem/ShoppingListItem';
import { ProductType } from '@/types';
import { usePreviouslyPurchased } from './hooks/usePreviouslyPurchased';
import { PreviouslyPurchasedItem } from './services/fetchPreviouslyPurchased';

// Helper function to convert API data to ProductType for ShoppingListItem
const convertToProductType = (item: PreviouslyPurchasedItem): ProductType => {
  return {
    id: item.productId,
    name: item.productName,
    imageUrl: item.imageUrl || null,
    isFavorite: false,
    manufacturer: item.vendorName,
    manufacturerSku: '',
    description: null,
    attributes: [],
    isHazardous: false,
    requiresPrescription: false,
    requiresColdShipping: false,
    isControlledSubstance: false,
    isControlled222Form: false,
    requiresPedigree: false,
    offers: [
      {
        id: item.productOfferId,
        vendor: {
          id: item.vendorId,
          name: item.vendorName,
          imageUrl: '',
          type: 'manufacturer' as const,
        },
        name: item.productName,
        vendorSku: '',
        price: (item.price / 100).toFixed(2), // Convert from cents to dollars
        clinicPrice: (item.price / 100).toFixed(2),
        stockStatus: item.stockStatus as any,
        lastOrderedAt: item.lastOrderedAt,
        lastOrderedQuantity: item.quantity,
        isPurchasable: item.isPurchasable,
        increments: item.increments,
        isRecommended: false,
        rebatePercent: null,
        product: {} as ProductType, // Will be set after creation
        unitOfMeasure: '',
        size: 0,
      },
    ],
  };
};

export const PreviouslyPurchased = () => {
  const {
    items,
    isLoading,
    total,
    currentPage,
    lastPage,
    searchQuery,
    perPage,
    setSearchQuery,
    setPerPage,
    setPage,
  } = usePreviouslyPurchased();

  // Convert API items to ProductType for ShoppingListItem
  const displayedItems = items.map((item) => {
    const product = convertToProductType(item);
    // Set the product reference for the offer
    product.offers[0].product = product;
    return product;
  });

  const handleSearch = (value: string) => {
    setSearchQuery(value);
  };

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handleItemsPerPageChange = (newItemsPerPage: string) => {
    setPerPage(parseInt(newItemsPerPage, 10));
  };

  if (isLoading) {
    return (
      <div className="w-full">
        <div className="mb-6 flex items-center justify-between rounded-lg border border-black/[0.06] bg-white p-5">
          <div className="flex flex-col gap-1">
            <h2 className="text-sm font-semibold">
              Your Previously Purchased Items
            </h2>
            <div className="flex items-center gap-4 text-xs text-black/50">
              <span>Loading...</span>
            </div>
          </div>
          <HeaderSearch
            placeholder="Search item"
            value={searchQuery}
            onChange={handleSearch}
            onEnter={handleSearch}
            rootClassName="w-80"
          />
        </div>
        <div className="mb-6 flex flex-col gap-1 rounded-lg bg-[#F8FBFD] p-4">
          <div className="flex items-center justify-center py-8">
            <span className="text-gray-500">Loading previously purchased items...</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="mb-6 flex items-center justify-between rounded-lg border border-black/[0.06] bg-white p-5">
        <div className="flex flex-col gap-1">
          <h2 className="text-sm font-semibold">
            Your Previously Purchased Items
          </h2>
          <div className="flex items-center gap-4 text-xs text-black/50">
            <span>
              Total of Items:{' '}
              <span className="font-semibold text-black/70">{total.toLocaleString()}</span>
            </span>
            {displayedItems.length > 0 && (
              <>
                <div className="divider-v"></div>
                <span>
                  Last Update:{' '}
                  <span className="font-semibold text-black/70">
                    {displayedItems[0].offers[0].lastOrderedAt
                      ? new Date(displayedItems[0].offers[0].lastOrderedAt).toLocaleDateString()
                      : 'N/A'
                    }
                  </span>
                </span>
              </>
            )}
          </div>
        </div>

        <HeaderSearch
          placeholder="Search item"
          value={searchQuery}
          onChange={handleSearch}
          onEnter={handleSearch}
          rootClassName="w-80"
        />
      </div>

      <div className="mb-6 flex flex-col gap-1 rounded-lg bg-[#F8FBFD] p-4">
        {displayedItems.length === 0 ? (
          <div className="flex items-center justify-center py-8">
            <span className="text-gray-500">
              {searchQuery ? 'No items found matching your search.' : 'No previously purchased items found.'}
            </span>
          </div>
        ) : (
          displayedItems.map((item) => (
            <ShoppingListItem
              key={item.id}
              product={item}
              variant="previouslyPurchased"
              showPromoIcon={false}
              showBadge={false}
              isHighlighted={
                !!searchQuery &&
                item.name.toLowerCase().includes(searchQuery.toLowerCase())
              }
            />
          ))
        )}
      </div>

      {total > 0 && (
        <Pagination
          page={currentPage}
          onPageChange={handlePageChange}
          onChangeItemsPerPage={handleItemsPerPageChange}
          itemsPerPage={perPage.toString()}
          limitOptions={[
            { value: '12', label: '12' },
            { value: '20', label: '20' },
            { value: '24', label: '24' },
            { value: '48', label: '48' },
          ]}
          total={total}
        />
      )}
    </div>
  );
};
