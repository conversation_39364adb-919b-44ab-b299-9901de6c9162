import { useState } from 'react';
import { ProductType } from '@/types';
import { CustomListItemActions } from '../../CustomLists/components/CustomList/components/CustomListItem/components/CustomListItemActions/CustomListItemActions';
import { Badge } from '@/libs/ui/Badge/Badge';
import { Dollar } from '@/libs/products/components/Dollar/Dollar';
import { getProductUrl } from '@/apps/shop/routes/utils';
import { Link } from 'react-router-dom';
import { getProductOfferComputedData } from '@/libs/products/utils/getProductComputedData';
import { mergeClasses } from '@/utils';
import { PREDEFINED_COLORS } from '@/libs/ui/ColorPicker/ColorPicker';

type ShoppingListItemProps = {
  className?: string;
  product: ProductType;
  isHighlighted?: boolean;
  variant?: 'customList' | 'previouslyPurchased';
  showPromoIcon?: boolean;
  showBadge?: boolean;
  badgeText?: string;
  badgeColor?: string;
  promoTooltip?: string;
};

export const ShoppingListItem = ({
  className,
  product,
  isHighlighted = false,
  variant = 'customList',
  showPromoIcon = true,
  showBadge = true,
  badgeText = 'Surgery Room',
  badgeColor = '#ED1F22',
  promoTooltip = 'Promotion Type: Bogo',
}: ShoppingListItemProps) => {
  const [currentOffer, setCurrentOffer] = useState(product.offers[0]);
  const productUrl = getProductUrl(product.id, currentOffer.id);

  const handleSwapVendor = (newOfferId: string) => {
    setCurrentOffer(product.offers.find(({ id }) => newOfferId === id)!);
  };

  const { salePrice, originalPrice } =
    getProductOfferComputedData(currentOffer);

  const shouldShowPromoIcon = variant === 'customList' && showPromoIcon;
  const shouldShowBadge = variant === 'customList' && showBadge;

  return (
    <div
      className={mergeClasses(
        'flex w-full justify-between rounded-lg border border-black/[0.06] bg-white p-4 pr-2',
        isHighlighted &&
          'border-2 border-blue-500 bg-gradient-to-r from-blue-50 to-blue-100 shadow-md',
        className,
      )}
    >
      <div className="flex flex-col justify-center gap-1">
        {(shouldShowPromoIcon || shouldShowBadge) && (
          <div className="mr-auto flex items-center gap-2">
            {shouldShowPromoIcon && (
              <Dollar toolTipLabel={promoTooltip} size="0.7rem" />
            )}
            {shouldShowBadge && (
              <Badge
                className={`h-4 text-[10px] text-white ${PREDEFINED_COLORS.find((color) => color.value === badgeColor)?.bgClass}`}
              >
                {badgeText}
              </Badge>
            )}
          </div>
        )}

        <Link to={productUrl} className="mr-4 max-w-md text-sm font-semibold">
          {product.name}
        </Link>

        <div className="mr-auto flex">
          <span className="text-xs font-semibold">
            {currentOffer.vendor.name}
          </span>
          <div className="divider-v"></div>
          <span className="text-xs text-black/50">
            SKU: <span className="text-black">{currentOffer.vendorSku}</span>
          </span>
          <div className="divider-v"></div>
          <span className="text-xs text-black/50">
            Per unit:{' '}
            {originalPrice > salePrice ? (
              <>
                <span className="mx-1 text-xs font-semibold text-black">
                  ${salePrice}
                </span>
                <span className="text-sxs line-through">${originalPrice}</span>
              </>
            ) : (
              <span className="mx-1 text-xs font-semibold text-black">
                ${salePrice}
              </span>
            )}{' '}
          </span>
        </div>
      </div>

      <CustomListItemActions
        product={product}
        originalPrice={originalPrice}
        salePrice={salePrice}
        currentOffer={currentOffer}
        onSwap={handleSwapVendor}
      />
    </div>
  );
};
