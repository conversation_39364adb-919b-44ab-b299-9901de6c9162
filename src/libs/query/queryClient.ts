import { QueryClient } from '@tanstack/react-query';

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000,
      gcTime: 10 * 60 * 1000,
    },
  },
});

export const queryKeys = {
  dashboard: {
    all: ['dashboard'] as const,
    metrics: {
      total: (query: string) =>
        [...queryKeys.dashboard.all, 'metrics/total-spend', query] as const,
    },
  },
  products: {
    all: ['products'] as const,
    details: (id: string | undefined) => [
      ...queryKeys.products.all,
      'details',
      id,
    ],
    alternatives: (productId: string, clinicId: string) => [
      ...queryKeys.products.all,
      'alternatives',
      productId,
      clinicId,
    ],
    promotions: (id: string | undefined) => [
      ...queryKeys.products.all,
      'promotions',
      id,
    ],
    suggestions: (clinicId: string, query: string) => [
      ...queryKeys.products.all,
      'suggestions',
      clinicId,
      query,
    ],
    search: (clinicId: string, searchParams: string) => [
      ...queryKeys.products.all,
      'search',
      clinicId,
      searchParams,
    ],
    previouslyPurchased: (clinicId: string, params: any) => [
      ...queryKeys.products.all,
      'previously-purchased',
      clinicId,
      params,
    ],
  },
  promotions: {
    all: ['promotions'] as const,
    buyXGetY: () => [...queryKeys.promotions.all, '?type=buy_x_get_y'],
  },
  rebates: {
    all: ['rebates'] as const,
    estimates: () => [...queryKeys.rebates.all, 'estimates'],
  },
  savedItems: {
    all: ['saved-items'] as const,
  },
  orders: {
    all: ['orders'] as const,
    details: (id: string) => [...queryKeys.orders.all, 'details', id] as const,
  },
} as const;
