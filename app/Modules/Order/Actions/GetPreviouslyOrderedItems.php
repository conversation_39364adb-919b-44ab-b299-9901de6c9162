<?php

declare(strict_types=1);

namespace App\Modules\Order\Actions;

use App\Enums\ProductStockStatus;
use App\Models\OrderItem;
use App\Modules\Integration\Enums\IntegrationConnectionStatus;
use App\Modules\Order\Data\PreviouslyOrderedItem;
use Carbon\Carbon;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

final class GetPreviouslyOrderedItems
{
    /**
     * Get the previously ordered items for a clinic.
     *
     * The items are ordered by:
     * 1. Latest purchase date (first priority)
     * 2. Number of times ordered (second priority if multiple items have the same latest purchase date)
     * 3. Supports both simple limit and pagination
     * 4. Optional search filter
     * 5. Only includes products from vendors with active connections to the clinic
     */
    public function handle(
        string $clinicId,
        ?string $searchQuery = null,
        int $limit = 3,
        bool $paginate = false,
        int $perPage = 20
    ): Collection|LengthAwarePaginator
    {

        $query = OrderItem::query()
            ->select([
                'product_offers.id as product_offer_id',
                'products.id as product_id',
                'products.name as product_name',
                'products.image_url',
                'vendors.id as vendor_id',
                'vendors.name as vendor_name',
                'product_offers.stock_status',
                'product_offers.increments',
                'product_offers.deactivated_at',
                DB::raw('MAX(order_items.created_at) as last_ordered_at'),
                DB::raw('SUM(order_items.quantity) as total_quantity'),
                DB::raw('COUNT(DISTINCT orders.id) as order_count'),
                'order_items.price',
            ])
            ->join('orders', 'orders.id', '=', 'order_items.order_id')
            ->join('product_offers', 'product_offers.id', '=', 'order_items.product_offer_id')
            ->join('products', 'products.id', '=', 'product_offers.product_id')
            ->join('vendors', 'vendors.id', '=', 'product_offers.vendor_id')
            ->join('integration_connections', function ($join) use ($clinicId) {
                $join->on('integration_connections.vendor_id', '=', 'vendors.id')
                    ->where('integration_connections.clinic_id', $clinicId)
                    ->where('integration_connections.status', IntegrationConnectionStatus::Connected->value);
            })
            ->where('orders.clinic_id', $clinicId);

        if ($searchQuery && !empty(trim($searchQuery))) {
            $searchTerms = explode(' ', $searchQuery);
            foreach ($searchTerms as $term) {
                $term = mb_trim($term);
                if (!empty($term)) {
                    $query->where(function ($q) use ($term) {
                        $q->where('products.name', 'ilike', "%{$term}%")
                            ->orWhere('product_offers.name', 'ilike', "%{$term}%");
                    });
                }
            }
        }

        $query->groupBy([
            'product_offers.id',
            'products.id',
            'products.name',
            'products.image_url',
            'vendors.id',
            'vendors.name',
            'product_offers.stock_status',
            'product_offers.increments',
            'order_items.price',
        ]);

        $query->orderByRaw('MAX(order_items.created_at) DESC')
            ->orderByRaw('COUNT(DISTINCT orders.id) DESC');

        if ($paginate) {
            $results = $query->paginate($perPage);

            $results->getCollection()->transform(function ($item) {
                return $this->transformItem($item);
            });

            return $results;
        } else {
            $results = $query->limit($limit)->get();

            return $results->map(function ($item) {
                return $this->transformItem($item);
            });
        }
    }

    private function transformItem($item): PreviouslyOrderedItem
    {
        return new PreviouslyOrderedItem(
            $item->product_offer_id,
            $item->product_id,
            $item->product_name,
            $item->vendor_id,
            $item->vendor_name,
            Carbon::parse($item->last_ordered_at),
            $item->total_quantity,
            $item->price,
            $item->image_url ?? '',
            $item->order_count,
            $status = ProductStockStatus::from($item->stock_status),
            $item->increments,
            $item->deactivated_at === null && $status !== ProductStockStatus::Discontinued,
        );
    }
}
