<?php

declare(strict_types=1);

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

final class GetPreviouslyPurchasedRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'search' => 'nullable|string|max:255',
            'per_page' => 'nullable|integer|min:1|max:100',
            'sort_by' => 'nullable|string|in:last_ordered,frequency,name',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'per_page.min' => 'The per page value must be at least 1.',
            'per_page.max' => 'The per page value may not be greater than 100.',
            'sort_by.in' => 'The sort by field must be one of: last_ordered, frequency, name.',
        ];
    }

    /**
     * Get the search query.
     */
    public function getSearch(): ?string
    {
        $search = $this->string('search')->toString();
        return empty($search) ? null : $search;
    }

    /**
     * Get the per page value.
     */
    public function getPerPage(): int
    {
        return $this->integer('per_page', 20);
    }

    /**
     * Get the sort by value.
     */
    public function getSortBy(): string
    {
        return $this->string('sort_by', 'last_ordered')->toString();
    }
}
