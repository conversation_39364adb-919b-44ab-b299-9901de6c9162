<?php

declare(strict_types=1);

namespace App\Http\Controllers\Clinics;

use App\Http\Controllers\Controller;
use App\Http\Requests\GetPreviouslyPurchasedRequest;
use App\Http\Resources\PreviouslyOrderedItemResource;
use App\Models\Clinic;
use App\Modules\Order\Actions\GetPreviouslyOrderedItems;
use Illuminate\Http\JsonResponse;

final class PreviouslyPurchasedController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct(
        private readonly GetPreviouslyOrderedItems $getPreviouslyOrderedItems,
    ) {
        //
    }

    /**
     * Get previously purchased products for shopping list.
     */
    public function index(GetPreviouslyPurchasedRequest $request, Clinic $clinic): JsonResponse
    {
        $previouslyOrderedItems = $this->getPreviouslyOrderedItems->handle(
            clinicId: $clinic->id,
            searchQuery: $request->getSearch(),
            limit: $request->getPerPage(),
            paginate: true
        );

        return PreviouslyOrderedItemResource::collection($previouslyOrderedItems)
            ->response();
    }
}
