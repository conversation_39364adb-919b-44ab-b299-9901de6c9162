<?php

declare(strict_types=1);

namespace App\Http\Controllers\Clinics;

use App\Http\Controllers\Controller;
use App\Http\Resources\PreviouslyOrderedItemResource;
use App\Models\Clinic;
use App\Modules\Order\Actions\GetPreviouslyOrderedItems;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

final class ShoppingListController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct(
        private readonly GetPreviouslyOrderedItems $getPreviouslyOrderedItems,
    ) {
        //
    }

    public function index(Request $request, Clinic $clinic): JsonResponse
    {
        $request->validate([
            'query' => 'required|string',
        ]);

        $searchQuery = $request->string('query')->toString();

        $products = $this->engine->handle(
            $clinic,
            $searchQuery,
        );

        // Get previously ordered items that match the search query
        $previouslyOrderedItems = $this->getPreviouslyOrderedItems->handle($clinic->id, $searchQuery, 3);

        return response()->json([
            'data' => $products,
            'previously_ordered_items' => PreviouslyOrderedItemResource::collection($previouslyOrderedItems),
        ]);
    }
}
